<?php
/**
 * Debug script for variation images feature
 * Add this to your WordPress root and access via browser to debug
 */

// Load WordPress
require_once('wp-config.php');

// Check if we have a product ID
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

if (!$product_id) {
    echo '<h1>RIDCOD Variation Images Debug</h1>';
    echo '<p>Please provide a product ID: <code>?product_id=123</code></p>';
    
    // Show some variable products
    $products = get_posts(array(
        'post_type' => 'product',
        'meta_query' => array(
            array(
                'key' => '_product_type',
                'value' => 'variable'
            )
        ),
        'posts_per_page' => 10
    ));
    
    if ($products) {
        echo '<h2>Available Variable Products:</h2>';
        echo '<ul>';
        foreach ($products as $product) {
            echo '<li><a href="?product_id=' . $product->ID . '">' . $product->post_title . ' (ID: ' . $product->ID . ')</a></li>';
        }
        echo '</ul>';
    }
    exit;
}

// Get the product
$product = wc_get_product($product_id);

if (!$product || !$product->is_type('variable')) {
    echo '<h1>Error</h1>';
    echo '<p>Product not found or not a variable product.</p>';
    exit;
}

echo '<h1>RIDCOD Variation Images Debug</h1>';
echo '<h2>Product: ' . $product->get_name() . ' (ID: ' . $product_id . ')</h2>';

// Check settings
$show_variation_images = get_option('rid_cod_show_variation_images', 'no') === 'yes';
echo '<h3>Settings:</h3>';
echo '<p><strong>Show Variation Images:</strong> ' . ($show_variation_images ? 'YES' : 'NO') . '</p>';

if (!$show_variation_images) {
    echo '<p style="color: red;"><strong>The variation images feature is DISABLED!</strong></p>';
    echo '<p>Go to WooCommerce > RIDCOD - Ecom > Form Control Settings and enable "عرض صور المنتج في متغيرات اللون"</p>';
}

// Get variations
$available_variations = $product->get_available_variations();
echo '<h3>Available Variations (' . count($available_variations) . '):</h3>';

if (empty($available_variations)) {
    echo '<p style="color: red;">No variations found!</p>';
    exit;
}

// Test the get_variation_images function
require_once(ABSPATH . 'wp-content/plugins/RIDCOD/includes/class-rid-cod-form.php');

// Create a reflection to access private method
$form = new RID_COD_Form();
$reflection = new ReflectionClass($form);
$method = $reflection->getMethod('get_variation_images');
$method->setAccessible(true);

echo '<h3>Testing get_variation_images function...</h3>';
$variation_images = $method->invoke($form, $product);

// Also test the decode function
echo '<h3>Testing rid_cod_decode_variation_attribute function:</h3>';
$test_values = array('red', 'أحمر', 'pa_color', 'attribute_pa_color', urlencode('أحمر'));
foreach ($test_values as $test_value) {
    $decoded = rid_cod_decode_variation_attribute($test_value);
    echo '<p>' . $test_value . ' → ' . $decoded . '</p>';
}

echo '<h3>Extracted Variation Images (' . count($variation_images) . '):</h3>';

if (empty($variation_images)) {
    echo '<p style="color: red;">No variation images found!</p>';
    
    echo '<h4>Debugging Information:</h4>';
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Variation ID</th><th>Attributes</th><th>Has Image</th><th>Image URL</th></tr>';
    
    foreach ($available_variations as $variation) {
        echo '<tr>';
        echo '<td>' . $variation['variation_id'] . '</td>';
        echo '<td><pre>' . print_r($variation['attributes'], true) . '</pre></td>';
        echo '<td>' . (isset($variation['image']['src']) && !empty($variation['image']['src']) ? 'YES' : 'NO') . '</td>';
        echo '<td>' . (isset($variation['image']['src']) ? $variation['image']['src'] : 'N/A') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
} else {
    echo '<table border="1" cellpadding="5">';
    echo '<tr><th>Color Value</th><th>Image</th><th>Variation ID</th></tr>';
    
    foreach ($variation_images as $color_value => $image_data) {
        echo '<tr>';
        echo '<td>' . esc_html($color_value) . '</td>';
        echo '<td><img src="' . esc_url($image_data['src']) . '" style="max-width: 100px; max-height: 100px;"></td>';
        echo '<td>' . $image_data['variation_id'] . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}

// Show color attributes
$attributes = $product->get_variation_attributes();
echo '<h3>Product Attributes:</h3>';
echo '<pre>' . print_r($attributes, true) . '</pre>';

echo '<h3>Color Attribute Detection:</h3>';
$color_attributes = array('color', 'colour', 'لون', 'اللون', 'pa_color', 'pa_colour', 'pa_لون', 'pa_اللون');
$found_color_attributes = array();

foreach ($attributes as $attr_name => $attr_values) {
    $clean_attr_name = str_replace('attribute_', '', $attr_name);
    foreach ($color_attributes as $color_attr) {
        if (stripos($clean_attr_name, $color_attr) !== false) {
            $found_color_attributes[] = $attr_name . ' (matches: ' . $color_attr . ')';
        }
    }
}

if (empty($found_color_attributes)) {
    echo '<p style="color: red;">No color attributes detected!</p>';
    echo '<p>Available attributes: ' . implode(', ', array_keys($attributes)) . '</p>';
    echo '<p>Looking for attributes containing: ' . implode(', ', $color_attributes) . '</p>';
} else {
    echo '<p style="color: green;">Found color attributes:</p>';
    echo '<ul>';
    foreach ($found_color_attributes as $attr) {
        echo '<li>' . $attr . '</li>';
    }
    echo '</ul>';
}
?>
